# frozen_string_literal: true

module V1
  class KnowledgeBaseFileOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        description: @object.description,
        file_url: @object.file_url,
        filename: @object.filename,
        content_type: @object.content_type,
        file_size: @object.file_size,
        is_active: @object.is_active,
        file_type: @object.file_type,
        download_url: @object.download_url,
        organization_id: @object.organization_id,
        file_id: @object.openai_file&.id
      }
    end

    def plan_limit_format
      {
        max_files: @object.max_files,
        current_files: @object.current_files,
        remaining_files: @object.remaining_files
      }
    end
  end
end
