# frozen_string_literal: true

class KnowledgeBaseFile < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization
  has_one :openai_file, as: :object

  # Ragie.ai status constants
  RAGIE_STATUSES = %w[pending processing ready failed].freeze

  scope :active, -> { where(is_active: true) }
  scope :by_organization, ->(organization_id) { where(organization_id: organization_id) }

  def file_extension
    File.extname(filename).downcase if filename.present?
  end

  def is_pdf?
    file_extension == '.pdf'
  end

  def is_text?
    %w[.txt .md .doc .docx].include?(file_extension)
  end

  def is_image?
    %w[.jpg .jpeg .png .gif .webp].include?(file_extension)
  end

  def file_type
    if is_pdf?
      'pdf'
    elsif is_text?
      'text'
    elsif is_image?
      'image'
    else
      'other'
    end
  end

  def download_url
    return file_url if file_url.present?

    nil
  rescue StandardError => e
    Rails.logger.error "Error generating download URL: #{e.message}"
    nil
  end
end
