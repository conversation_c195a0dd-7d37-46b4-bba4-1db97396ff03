# frozen_string_literal: true

class UploadRagDocumentService < AppService
  def upload_document(params)
    membership = @user.membership
    organization_id = membership.organization_id
    document_type = params.delete(:document_type)

    @rag_service = ::External::RagService.new(organization_id)
    response = @rag_service.upload_document(params)

    file = OpenaiFile.create!(
      object_id: SecureRandom.uuid,
      object_class: 'RagDocument',
      object_class_column: document_type,
      openai_file_id: response['data']['id']
    )

    OpenStruct.new(
      id: file.id,
      document_id: response['data']['id']
    )
  end

  def status(document_id)
    membership = @user.membership
    organization_id = membership.organization_id

    @rag_service = ::External::RagService.new(organization_id)
    response = @rag_service.status(document_id)

    OpenStruct.new(
      id: document_id,
      status: response['data']['status']
    )
  end
end
