require_relative 'routes_helpers'
require 'sidekiq/web'
require 'sidekiq/cron/web'

Rails.application.routes.draw do
  extend RoutesHelpers

  namespace :v1 do
    # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html
    # post 'stream', to: 'openai#stream_response'
    post 'stream_v2', to: 'openai#stream_response_v2'
    # post 'chat_stream', to: 'openai#chat_stream'
    post 'chat_stream_v2', to: 'openai#chat_stream_v2'

    resources :chats, only: %i[create index destroy update]

    post 'workspaces/:workspace_id/chats', to: 'chats#create'
    put 'workspaces/:workspace_id/chats/:id', to: 'chats#update'
    get 'workspaces/:workspace_id/chats', to: 'chats#list'
    get 'workspaces/:workspace_id/chats/:chat_id', to: 'chats#show'
    delete 'workspaces/:workspace_id/chats/:chat_id', to: 'chats#delete'
    get 'chats/share/:external_id', to: 'public#show_chat'

    get 'workspaces/:workspace_id', to: 'openai#show_workspace'

    post 'model_template_variables', to: 'model_template_variables#create'
    put 'model_template_variables/:id', to: 'model_template_variables#update'
    get 'model_template_variables', to: 'model_template_variables#list'
    delete 'model_template_variables/:id', to: 'model_template_variables#destroy'

    get 's3/uploads/presign', to: 's3#upload_s3'

    resources :model_templates, only: %i[create update show index destroy] do
      post 'duplicate', to: 'model_templates#duplicate'
      get 'comments', to: 'model_templates#list_comments'

      # Bank operations
      post 'add_to_bank', to: 'model_templates#add_to_bank'
      delete 'remove_from_bank', to: 'model_templates#remove_from_bank'

      # Parent-child operations
      post 'duplicate_to_organizations', to: 'model_templates#duplicate_to_organizations', on: :collection

      get ':id/system_prompt', to: 'model_templates#system_prompt', on: :collection
    end

    resources :parent_templates, only: [] do
      collection do
        put ':id', to: 'model_template_banks#update_parent_template'
        delete ':id', to: 'model_template_banks#destroy_parent_template'
        post ':id/duplicate', to: 'model_template_banks#duplicate_parent_template'
        post ':id/assign', to: 'model_template_banks#assign_parent_template'
      end
    end

    resources :template_categories, only: %i[create update show index destroy]
    resources :memberships, only: %i[index update destroy]

    resources :users, only: %i[show update] do
      post ':id/change_password', to: 'users#change_password', on: :collection
    end

    resources :model_ratings, only: %i[create update index destroy] do
      get 'results', to: 'model_ratings#model_rating_results', on: :collection
    end

    resources :knowledge_base_files, only: %i[create update show index destroy] do
      get 'plan_limits', to: 'knowledge_base_files#plan_limits', on: :collection
    end
    resources :organizations, only: %i[create update show index destroy] do
      post 'transfer_ownership', to: 'organizations#transfer_ownership'
      get 'remaining_tokens', to: 'organizations#remaining_tokens'
    end
    resources :organization_teams, only: %i[create update show index destroy]
    resources :model_template_ins, only: %i[create update index destroy]
    resources :store_items, only: %i[create update show index destroy]
    resources :invoices, only: %i[create show index]
    post 'invoices/handle_callback', to: 'invoices#handle_callback'

    resources :user_managements, only: %i[] do
      collection do
        post 'invite', to: 'user_managements#create_invitation'
        post 'accept_invitation', to: 'user_managements#accept_invitation'
        get 'invitation_code_details', to: 'user_managements#detail_invitation'
        post 'change_password_request', to: 'user_managements#change_password_request'
        post 'change_password', to: 'user_managements#change_password'
        get 'invited_users', to: 'user_managements#list_invited_users'
      end
    end

    post 'login', to: 'auth#authenticate'
    get 'auth', to: 'auth#show'
    get 'users_get_by_email', to: 'users#get_by_email'

    get 'citations/:id', to: 'citations#show'
    # Platform Role Management
    get 'platform_roles/admins', to: 'platform_roles#list_admin_platform_roles'

    resources :agent_workflows, only: %i[create update show index destroy]
    resources :agent_workflow_nodes, only: %i[create update destroy]
    resources :prompt_evals, only: %i[show index] do
      post 'evaluate', to: 'prompt_evals#evaluate', on: :collection
    end

    resources :prompt_eval_results, only: %i[show index]
    resources :model_banks, only: %i[index]
  end

  mount Sidekiq::Web => '/sidekiq'

  # Defines the root path route ("/")
  #################
  # INSTRUMENTATION ENDPOINTS
  #################

  get 'version' => proc { |_env|
    rack_json(version: CollabwayApi::VERSION, up_since: CollabwayApi::UP_SINCE)
  }
end
