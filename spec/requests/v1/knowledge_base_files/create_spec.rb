# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#create', type: :request do
  let!(:user) do
    User.create!(
      display_name: 'Test User',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization', code: 'test-org')
  end

  let!(:membership) do
    Membership.create!(
      user_id: user.id,
      organization_id: organization.id,
      role: 0 # member
    )
  end

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let!(:openai_file) do
    OpenaiFile.create!(
      id: 'file-abc123',
      filename: 'test.pdf',
      purpose: 'assistants',
      bytes: 1024
    )
  end

  let(:valid_params) do
    {
      name: 'Test Knowledge Base',
      description: 'Test description',
      filename: 'test.pdf',
      file_url: 'https://example.com/test.pdf',
      content_type: 'application/pdf',
      file_size: 1024,
      file_id: openai_file.id
    }
  end

  let(:minimal_params) do
    {
      name: 'Minimal Knowledge Base',
      filename: 'minimal.pdf',
      file_id: openai_file.id
    }
  end

  describe 'POST /v1/knowledge_base_files' do
    context 'with valid parameters' do
      it 'creates a new knowledge base file with all fields' do
        post '/v1/knowledge_base_files', valid_params, as_user(user)
        expect_response(:created)

        expect(response_data['name']).to eq('Test Knowledge Base')
        expect(response_data['description']).to eq('Test description')
        expect(response_data['filename']).to eq('test.pdf')
        expect(response_data['file_url']).to eq('https://example.com/test.pdf')
        expect(response_data['content_type']).to eq('application/pdf')
        expect(response_data['file_size']).to eq(1024)
        expect(response_data['organization_id']).to eq(organization.id)
        expect(response_data['file_id']).to eq(openai_file.id)
      end

      it 'creates a new knowledge base file with minimal required fields' do
        post '/v1/knowledge_base_files', minimal_params, as_user(user)
        expect_response(:created)

        expect(response_data['name']).to eq('Minimal Knowledge Base')
        expect(response_data['filename']).to eq('minimal.pdf')
        expect(response_data['organization_id']).to eq(organization.id)
        expect(response_data['file_id']).to eq(openai_file.id)
      end

      it 'associates the OpenAI file with the knowledge base file' do
        post '/v1/knowledge_base_files', valid_params, as_user(user)
        expect_response(:created)

        openai_file.reload
        expect(openai_file.object_id).to eq(response_data['id'])
        expect(openai_file.object_class).to eq('KnowledgeBaseFile')
        expect(openai_file.object_class_column).to eq('file_url')
      end

      it 'creates knowledge base file for different organization when organization_id is provided' do
        other_organization = Organization.create!(name: 'Other Org', code: 'other-org')
        
        # Create admin user who can create files for other organizations
        admin_user = User.create!(
          display_name: 'Admin User',
          email: '<EMAIL>',
          password: '12345678'
        )
        
        admin_membership = Membership.create!(
          user_id: admin_user.id,
          organization_id: organization.id,
          role: 1, # admin
          membership_role: 'owner'
        )

        params_with_org = valid_params.merge(organization_id: other_organization.id)
        
        post '/v1/knowledge_base_files', params_with_org, as_user(admin_user)
        expect_response(:created)

        expect(response_data['organization_id']).to eq(other_organization.id)
      end
    end

    context 'with invalid parameters' do
      it 'returns validation errors when name is missing' do
        invalid_params = valid_params.except(:name)
        post '/v1/knowledge_base_files', invalid_params, as_user(user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation errors when filename is missing' do
        invalid_params = valid_params.except(:filename)
        post '/v1/knowledge_base_files', invalid_params, as_user(user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation errors when name is empty' do
        invalid_params = valid_params.merge(name: '')
        post '/v1/knowledge_base_files', invalid_params, as_user(user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation errors when filename is empty' do
        invalid_params = valid_params.merge(filename: '')
        post '/v1/knowledge_base_files', invalid_params, as_user(user)
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'when plan limit is exceeded' do
      before do
        # Create files up to the limit
        10.times do |i|
          KnowledgeBaseFile.create!(
            organization_id: organization.id,
            name: "File #{i}",
            filename: "file#{i}.pdf",
            file_url: "https://example.com/file#{i}.pdf"
          )
        end
      end

      it 'returns plan limit error' do
        post '/v1/knowledge_base_files', valid_params, as_user(user)
        expect_error_response(:unprocessable_entity)
        
        expect(response_body[:errors][0][:message]).to include('Plan limit exceeded')
      end
    end

    context 'when OpenAI file does not exist' do
      it 'returns not found error' do
        invalid_params = valid_params.merge(file_id: 'non-existent-file')
        post '/v1/knowledge_base_files', invalid_params, as_user(user)
        expect_error_response(:not_found)
      end
    end

    context 'authorization' do
      it 'requires authentication' do
        post '/v1/knowledge_base_files', valid_params
        expect_error_response(:unauthorized)
      end

      it 'requires proper user role for creation' do
        # Create a user with insufficient permissions
        regular_user = User.create!(
          display_name: 'Regular User',
          email: '<EMAIL>',
          password: '12345678'
        )
        
        regular_membership = Membership.create!(
          user_id: regular_user.id,
          organization_id: organization.id,
          role: 0, # member
          membership_role: 'member'
        )

        post '/v1/knowledge_base_files', valid_params, as_user(regular_user)
        expect_error_response(:forbidden)
      end
    end

    context 'when organization does not exist' do
      it 'returns not found error for invalid organization_id' do
        params_with_invalid_org = valid_params.merge(organization_id: 99999)
        post '/v1/knowledge_base_files', params_with_invalid_org, as_user(user)
        expect_error_response(:not_found)
      end
    end
  end
end
