# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#update', type: :request do
  let!(:user) do
    User.create!(
      display_name: 'Test User',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization', code: 'test-org')
  end

  let!(:membership) do
    Membership.create!(
      user_id: user.id,
      organization_id: organization.id,
      role: 1, # admin role required for updates
      membership_role: 'team_admin'
    )
  end

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let!(:openai_file) do
    OpenaiFile.create!(
      id: 'file-abc123',
      filename: 'test.pdf',
      purpose: 'assistants',
      bytes: 1024
    )
  end

  let!(:new_openai_file) do
    OpenaiFile.create!(
      id: 'file-xyz789',
      filename: 'updated.pdf',
      purpose: 'assistants',
      bytes: 2048
    )
  end

  let!(:knowledge_base_file) do
    file = KnowledgeBaseFile.create!(
      organization_id: organization.id,
      name: 'Test File',
      description: 'Original description',
      filename: 'test.pdf',
      file_url: 'https://example.com/test.pdf',
      content_type: 'application/pdf',
      file_size: 1024,
      is_active: true
    )
    
    # Associate with OpenAI file
    openai_file.update!(
      object_id: file.id,
      object_class: 'KnowledgeBaseFile',
      object_class_column: 'file_url'
    )
    
    file
  end

  describe 'PUT /v1/knowledge_base_files/:id' do
    context 'with valid parameters' do
      it 'updates the knowledge base file name and description' do
        update_params = {
          name: 'Updated Name',
          description: 'Updated description'
        }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_response(:ok)

        expect(response_data['name']).to eq('Updated Name')
        expect(response_data['description']).to eq('Updated description')
        expect(response_data['filename']).to eq('test.pdf') # unchanged
      end

      it 'updates the file properties' do
        update_params = {
          filename: 'updated.pdf',
          content_type: 'application/vnd.ms-excel',
          file_size: 2048
        }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_response(:ok)

        expect(response_data['filename']).to eq('updated.pdf')
        expect(response_data['content_type']).to eq('application/vnd.ms-excel')
        expect(response_data['file_size']).to eq(2048)
      end

      it 'updates the active status' do
        update_params = { is_active: false }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_response(:ok)

        expect(response_data['is_active']).to eq(false)
      end

      it 'updates the file URL' do
        update_params = { file_url: 'https://example.com/updated.pdf' }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_response(:ok)

        expect(response_data['file_url']).to eq('https://example.com/updated.pdf')
      end

      it 'updates multiple fields at once' do
        update_params = {
          name: 'Completely Updated File',
          description: 'New description',
          filename: 'new_file.pdf',
          is_active: false,
          file_size: 3072
        }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_response(:ok)

        expect(response_data['name']).to eq('Completely Updated File')
        expect(response_data['description']).to eq('New description')
        expect(response_data['filename']).to eq('new_file.pdf')
        expect(response_data['is_active']).to eq(false)
        expect(response_data['file_size']).to eq(3072)
      end

      it 'updates the associated OpenAI file when file_id changes' do
        update_params = { file_id: new_openai_file.id }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_response(:ok)

        # Check that new file is associated
        new_openai_file.reload
        expect(new_openai_file.object_id).to eq(knowledge_base_file.id)
        expect(new_openai_file.object_class).to eq('KnowledgeBaseFile')

        # Check that old file is discarded
        openai_file.reload
        expect(openai_file.discarded?).to eq(true)
      end

      it 'does not change OpenAI file association when file_id is the same' do
        update_params = { 
          name: 'Updated Name',
          file_id: openai_file.id # same file_id
        }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_response(:ok)

        # Original file should still be associated and not discarded
        openai_file.reload
        expect(openai_file.object_id).to eq(knowledge_base_file.id)
        expect(openai_file.discarded?).to eq(false)
      end
    end

    context 'with invalid parameters' do
      it 'returns validation errors when name is empty' do
        update_params = { name: '' }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation errors when filename is empty' do
        update_params = { filename: '' }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_error_response(:unprocessable_entity)
      end

      it 'returns validation errors for invalid file_size' do
        update_params = { file_size: -1 }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_error_response(:unprocessable_entity)
      end
    end

    context 'with invalid knowledge base file id' do
      it 'returns not found error' do
        update_params = { name: 'Updated Name' }

        put '/v1/knowledge_base_files/99999', update_params, as_user(user)
        expect_error_response(:not_found)
      end
    end

    context 'when OpenAI file does not exist' do
      it 'returns not found error' do
        update_params = { file_id: 'non-existent-file' }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(user)
        expect_error_response(:not_found)
      end
    end

    context 'authorization' do
      it 'requires authentication' do
        update_params = { name: 'Updated Name' }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params
        expect_error_response(:unauthorized)
      end

      it 'requires proper user role for updates' do
        # Create a user with insufficient permissions
        regular_user = User.create!(
          display_name: 'Regular User',
          email: '<EMAIL>',
          password: '12345678'
        )
        
        regular_membership = Membership.create!(
          user_id: regular_user.id,
          organization_id: organization.id,
          role: 0, # member
          membership_role: 'member'
        )

        update_params = { name: 'Updated Name' }

        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", update_params, as_user(regular_user)
        expect_error_response(:forbidden)
      end

      it 'prevents updating files from other organizations' do
        other_organization = Organization.create!(name: 'Other Org', code: 'other-org')
        other_file = KnowledgeBaseFile.create!(
          organization_id: other_organization.id,
          name: 'Other File',
          filename: 'other.pdf',
          file_url: 'https://example.com/other.pdf'
        )

        update_params = { name: 'Hacked Name' }

        put "/v1/knowledge_base_files/#{other_file.id}", update_params, as_user(user)
        expect_error_response(:forbidden)
      end
    end

    context 'with empty update parameters' do
      it 'returns the unchanged knowledge base file' do
        put "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        expect(response_data['name']).to eq('Test File')
        expect(response_data['description']).to eq('Original description')
      end
    end
  end
end
