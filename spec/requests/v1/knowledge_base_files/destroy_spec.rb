# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#destroy', type: :request do
  let!(:user) do
    User.create!(
      display_name: 'Test User',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization', code: 'test-org')
  end

  let!(:membership) do
    Membership.create!(
      user_id: user.id,
      organization_id: organization.id,
      role: 1, # admin role required for deletion
      membership_role: 'team_admin'
    )
  end

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let!(:openai_file) do
    OpenaiFile.create!(
      id: 'file-abc123',
      filename: 'test.pdf',
      purpose: 'assistants',
      bytes: 1024
    )
  end

  let!(:knowledge_base_file) do
    file = KnowledgeBaseFile.create!(
      organization_id: organization.id,
      name: 'Test File',
      description: 'Test description',
      filename: 'test.pdf',
      file_url: 'https://example.com/test.pdf',
      content_type: 'application/pdf',
      file_size: 1024,
      is_active: true
    )
    
    # Associate with OpenAI file
    openai_file.update!(
      object_id: file.id,
      object_class: 'KnowledgeBaseFile',
      object_class_column: 'file_url'
    )
    
    file
  end

  describe 'DELETE /v1/knowledge_base_files/:id' do
    context 'with valid id' do
      it 'soft deletes the knowledge base file' do
        delete "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        # Verify the response contains the deleted file data
        expect(response_data['id']).to eq(knowledge_base_file.id)
        expect(response_data['name']).to eq('Test File')

        # Verify the file is soft deleted
        knowledge_base_file.reload
        expect(knowledge_base_file.discarded?).to eq(true)
      end

      it 'soft deletes the associated OpenAI file' do
        delete "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        # Verify the OpenAI file is also soft deleted
        openai_file.reload
        expect(openai_file.discarded?).to eq(true)
      end

      it 'handles deletion when no OpenAI file is associated' do
        file_without_openai = KnowledgeBaseFile.create!(
          organization_id: organization.id,
          name: 'File Without OpenAI',
          filename: 'no_openai.pdf',
          file_url: 'https://example.com/no_openai.pdf'
        )

        delete "/v1/knowledge_base_files/#{file_without_openai.id}", {}, as_user(user)
        expect_response(:ok)

        # Verify the file is soft deleted
        file_without_openai.reload
        expect(file_without_openai.discarded?).to eq(true)
      end

      it 'returns the deleted knowledge base file data' do
        delete "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        expect(response_data['id']).to eq(knowledge_base_file.id)
        expect(response_data['name']).to eq('Test File')
        expect(response_data['description']).to eq('Test description')
        expect(response_data['filename']).to eq('test.pdf')
        expect(response_data['file_url']).to eq('https://example.com/test.pdf')
        expect(response_data['organization_id']).to eq(organization.id)
      end

      it 'allows deletion of inactive files' do
        inactive_file = KnowledgeBaseFile.create!(
          organization_id: organization.id,
          name: 'Inactive File',
          filename: 'inactive.pdf',
          file_url: 'https://example.com/inactive.pdf',
          is_active: false
        )

        delete "/v1/knowledge_base_files/#{inactive_file.id}", {}, as_user(user)
        expect_response(:ok)

        inactive_file.reload
        expect(inactive_file.discarded?).to eq(true)
      end
    end

    context 'with invalid id' do
      it 'returns not found error for non-existent id' do
        delete '/v1/knowledge_base_files/99999', {}, as_user(user)
        expect_error_response(:not_found)
      end

      it 'returns not found error for invalid id format' do
        delete '/v1/knowledge_base_files/invalid', {}, as_user(user)
        expect_error_response(:not_found)
      end
    end

    context 'authorization' do
      it 'requires authentication' do
        delete "/v1/knowledge_base_files/#{knowledge_base_file.id}"
        expect_error_response(:unauthorized)
      end

      it 'requires proper user role for deletion' do
        # Create a user with insufficient permissions
        regular_user = User.create!(
          display_name: 'Regular User',
          email: '<EMAIL>',
          password: '12345678'
        )
        
        regular_membership = Membership.create!(
          user_id: regular_user.id,
          organization_id: organization.id,
          role: 0, # member
          membership_role: 'member'
        )

        delete "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(regular_user)
        expect_error_response(:forbidden)
      end

      it 'prevents deleting files from other organizations' do
        other_organization = Organization.create!(name: 'Other Org', code: 'other-org')
        other_file = KnowledgeBaseFile.create!(
          organization_id: other_organization.id,
          name: 'Other File',
          filename: 'other.pdf',
          file_url: 'https://example.com/other.pdf'
        )

        delete "/v1/knowledge_base_files/#{other_file.id}", {}, as_user(user)
        expect_error_response(:forbidden)
      end

      context 'with platform admin user' do
        let!(:platform_admin) do
          admin_user = User.create!(
            display_name: 'Platform Admin',
            email: '<EMAIL>',
            password: '12345678'
          )
          
          Membership.create!(
            user_id: admin_user.id,
            organization_id: organization.id,
            role: 1,
            membership_role: 'admin'
          )
          
          admin_user
        end

        it 'allows platform admin to delete files from any organization' do
          other_organization = Organization.create!(name: 'Other Org', code: 'other-org')
          other_file = KnowledgeBaseFile.create!(
            organization_id: other_organization.id,
            name: 'Other File',
            filename: 'other.pdf',
            file_url: 'https://example.com/other.pdf'
          )

          delete "/v1/knowledge_base_files/#{other_file.id}", {}, as_user(platform_admin)
          expect_response(:ok)

          other_file.reload
          expect(other_file.discarded?).to eq(true)
        end
      end

      context 'with partner admin user' do
        let!(:partner_admin) do
          admin_user = User.create!(
            display_name: 'Partner Admin',
            email: '<EMAIL>',
            password: '12345678',
            role: 'partner_admin'
          )
          
          Membership.create!(
            user_id: admin_user.id,
            organization_id: organization.id,
            role: 1
          )
          
          admin_user
        end

        it 'allows partner admin to delete files from organizations they created' do
          created_organization = Organization.create!(
            name: 'Created Org', 
            code: 'created-org',
            created_by_id: partner_admin.id.to_s
          )
          
          created_file = KnowledgeBaseFile.create!(
            organization_id: created_organization.id,
            name: 'Created File',
            filename: 'created.pdf',
            file_url: 'https://example.com/created.pdf'
          )

          delete "/v1/knowledge_base_files/#{created_file.id}", {}, as_user(partner_admin)
          expect_response(:ok)

          created_file.reload
          expect(created_file.discarded?).to eq(true)
        end

        it 'prevents partner admin from deleting files from organizations they did not create' do
          other_organization = Organization.create!(
            name: 'Other Org', 
            code: 'other-org',
            created_by_id: 'different-user-id'
          )
          
          other_file = KnowledgeBaseFile.create!(
            organization_id: other_organization.id,
            name: 'Other File',
            filename: 'other.pdf',
            file_url: 'https://example.com/other.pdf'
          )

          delete "/v1/knowledge_base_files/#{other_file.id}", {}, as_user(partner_admin)
          expect_error_response(:forbidden)
        end
      end
    end

    context 'with already discarded knowledge base file' do
      let!(:discarded_file) do
        file = KnowledgeBaseFile.create!(
          organization_id: organization.id,
          name: 'Discarded File',
          filename: 'discarded.pdf',
          file_url: 'https://example.com/discarded.pdf'
        )
        file.discard!
        file
      end

      it 'returns not found error for already discarded file' do
        delete "/v1/knowledge_base_files/#{discarded_file.id}", {}, as_user(user)
        expect_error_response(:not_found)
      end
    end
  end
end
