# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::KnowledgeBaseFiles#show', type: :request do
  let!(:user) do
    User.create!(
      display_name: 'Test User',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'Test Organization', code: 'test-org')
  end

  let!(:membership) do
    Membership.create!(
      user_id: user.id,
      organization_id: organization.id,
      role: 0 # member
    )
  end

  let!(:plan_threshold) do
    OrganizationsPlansThreshold.create!(
      organization_id: organization.id,
      max_knowledge_base_files: 10,
      monthly_credits_refresh: 1000,
      max_members: 10,
      max_workspaces: 5,
      purchased_credits: 500.0,
      remaining_monthly_credits: 200.0,
      refresh_date: 15
    )
  end

  let!(:openai_file) do
    OpenaiFile.create!(
      id: 'file-abc123',
      filename: 'test.pdf',
      purpose: 'assistants',
      bytes: 1024
    )
  end

  let!(:knowledge_base_file) do
    file = KnowledgeBaseFile.create!(
      organization_id: organization.id,
      name: 'Test File',
      description: 'Test description for the file',
      filename: 'test.pdf',
      file_url: 'https://example.com/test.pdf',
      content_type: 'application/pdf',
      file_size: 1024,
      is_active: true
    )
    
    # Associate with OpenAI file
    openai_file.update!(
      object_id: file.id,
      object_class: 'KnowledgeBaseFile',
      object_class_column: 'file_url'
    )
    
    file
  end

  let!(:inactive_knowledge_base_file) do
    KnowledgeBaseFile.create!(
      organization_id: organization.id,
      name: 'Inactive File',
      description: 'This file is inactive',
      filename: 'inactive.pdf',
      file_url: 'https://example.com/inactive.pdf',
      content_type: 'application/pdf',
      file_size: 512,
      is_active: false
    )
  end

  describe 'GET /v1/knowledge_base_files/:id' do
    context 'with valid id' do
      it 'returns the knowledge base file with all fields' do
        get "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        expect(response_data['id']).to eq(knowledge_base_file.id)
        expect(response_data['name']).to eq('Test File')
        expect(response_data['description']).to eq('Test description for the file')
        expect(response_data['filename']).to eq('test.pdf')
        expect(response_data['file_url']).to eq('https://example.com/test.pdf')
        expect(response_data['content_type']).to eq('application/pdf')
        expect(response_data['file_size']).to eq(1024)
        expect(response_data['is_active']).to eq(true)
        expect(response_data['organization_id']).to eq(organization.id)
        expect(response_data['file_id']).to eq(openai_file.id)
      end

      it 'returns inactive knowledge base file' do
        get "/v1/knowledge_base_files/#{inactive_knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        expect(response_data['id']).to eq(inactive_knowledge_base_file.id)
        expect(response_data['name']).to eq('Inactive File')
        expect(response_data['is_active']).to eq(false)
      end

      it 'returns file_type if available' do
        knowledge_base_file.update!(file_type: 'document')
        
        get "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        expect(response_data['file_type']).to eq('document')
      end

      it 'returns download_url if available' do
        # Assuming download_url is a method on the model
        allow(knowledge_base_file).to receive(:download_url).and_return('https://download.example.com/test.pdf')
        allow(KnowledgeBaseFile).to receive(:find).and_return(knowledge_base_file)
        
        get "/v1/knowledge_base_files/#{knowledge_base_file.id}", {}, as_user(user)
        expect_response(:ok)

        expect(response_data).to have_key('download_url')
      end

      it 'returns knowledge base file without OpenAI file association' do
        file_without_openai = KnowledgeBaseFile.create!(
          organization_id: organization.id,
          name: 'File Without OpenAI',
          filename: 'no_openai.pdf',
          file_url: 'https://example.com/no_openai.pdf'
        )

        get "/v1/knowledge_base_files/#{file_without_openai.id}", {}, as_user(user)
        expect_response(:ok)

        expect(response_data['id']).to eq(file_without_openai.id)
        expect(response_data['name']).to eq('File Without OpenAI')
        expect(response_data['file_id']).to be_nil
      end
    end

    context 'with invalid id' do
      it 'returns not found error for non-existent id' do
        get '/v1/knowledge_base_files/99999', {}, as_user(user)
        expect_error_response(:not_found)
      end

      it 'returns not found error for invalid id format' do
        get '/v1/knowledge_base_files/invalid', {}, as_user(user)
        expect_error_response(:not_found)
      end
    end

    context 'authorization' do
      it 'requires authentication' do
        get "/v1/knowledge_base_files/#{knowledge_base_file.id}"
        expect_error_response(:unauthorized)
      end

      it 'prevents accessing files from other organizations' do
        other_organization = Organization.create!(name: 'Other Org', code: 'other-org')
        other_file = KnowledgeBaseFile.create!(
          organization_id: other_organization.id,
          name: 'Other File',
          filename: 'other.pdf',
          file_url: 'https://example.com/other.pdf'
        )

        get "/v1/knowledge_base_files/#{other_file.id}", {}, as_user(user)
        expect_error_response(:forbidden)
      end

      context 'with platform admin user' do
        let!(:platform_admin) do
          admin_user = User.create!(
            display_name: 'Platform Admin',
            email: '<EMAIL>',
            password: '12345678'
          )
          
          Membership.create!(
            user_id: admin_user.id,
            organization_id: organization.id,
            role: 1,
            membership_role: 'admin'
          )
          
          admin_user
        end

        it 'allows platform admin to access files from any organization' do
          other_organization = Organization.create!(name: 'Other Org', code: 'other-org')
          other_file = KnowledgeBaseFile.create!(
            organization_id: other_organization.id,
            name: 'Other File',
            filename: 'other.pdf',
            file_url: 'https://example.com/other.pdf'
          )

          get "/v1/knowledge_base_files/#{other_file.id}", {}, as_user(platform_admin)
          expect_response(:ok)

          expect(response_data['id']).to eq(other_file.id)
          expect(response_data['name']).to eq('Other File')
        end
      end

      context 'with partner admin user' do
        let!(:partner_admin) do
          admin_user = User.create!(
            display_name: 'Partner Admin',
            email: '<EMAIL>',
            password: '12345678',
            role: 'partner_admin'
          )
          
          Membership.create!(
            user_id: admin_user.id,
            organization_id: organization.id,
            role: 1
          )
          
          admin_user
        end

        it 'allows partner admin to access files from organizations they created' do
          created_organization = Organization.create!(
            name: 'Created Org', 
            code: 'created-org',
            created_by_id: partner_admin.id.to_s
          )
          
          created_file = KnowledgeBaseFile.create!(
            organization_id: created_organization.id,
            name: 'Created File',
            filename: 'created.pdf',
            file_url: 'https://example.com/created.pdf'
          )

          get "/v1/knowledge_base_files/#{created_file.id}", {}, as_user(partner_admin)
          expect_response(:ok)

          expect(response_data['id']).to eq(created_file.id)
          expect(response_data['name']).to eq('Created File')
        end

        it 'prevents partner admin from accessing files from organizations they did not create' do
          other_organization = Organization.create!(
            name: 'Other Org', 
            code: 'other-org',
            created_by_id: 'different-user-id'
          )
          
          other_file = KnowledgeBaseFile.create!(
            organization_id: other_organization.id,
            name: 'Other File',
            filename: 'other.pdf',
            file_url: 'https://example.com/other.pdf'
          )

          get "/v1/knowledge_base_files/#{other_file.id}", {}, as_user(partner_admin)
          expect_error_response(:forbidden)
        end
      end
    end

    context 'with discarded knowledge base file' do
      let!(:discarded_file) do
        file = KnowledgeBaseFile.create!(
          organization_id: organization.id,
          name: 'Discarded File',
          filename: 'discarded.pdf',
          file_url: 'https://example.com/discarded.pdf'
        )
        file.discard!
        file
      end

      it 'returns not found error for discarded file' do
        get "/v1/knowledge_base_files/#{discarded_file.id}", {}, as_user(user)
        expect_error_response(:not_found)
      end
    end
  end
end
